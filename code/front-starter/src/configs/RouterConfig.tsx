import LoginPage from 'src/pages/auth/login.page';
import PlanPage from 'src/pages/plan/plan.page';
import WorkflowPage from 'src/pages/workflow/workflow.page';
import { CheckoutPage } from 'src/pages/checkout/checkout';
import RegisterPage from 'src/pages/auth/register.page';
import SettingsPage from 'src/pages/settings/settings.page';
import AccountPage from 'src/pages/account/account.page';
import ProfilePage from 'src/pages/profile/profile.page';
import { AuthTokenPage } from 'src/pages/auth/authToken.page';
import HomePage from 'src/pages/home/<USER>';
import DashboardPage from 'src/pages/dashboard';
import DataTableExamplePage from 'src/pages/data-table-example/data-table-example.page';
import NotificationsPage from 'src/pages/notifications/notifications.page';
import ActivityLogPage from 'src/pages/account/activity-log';
import SetupWizardPage from 'src/pages/setup/setup.page';
import OnboardingPage from 'src/pages/onboarding/onboarding.page';

// Legal pages
import LegalLayout from 'src/pages/legal/legal.layout';
import TermsPage from 'src/pages/legal/terms.page';
import PrivacyPage from 'src/pages/legal/privacy.page';

// Error pages
import NotFoundPage from 'src/pages/error/not-found.page';
import ServerErrorPage from 'src/pages/error/server-error.page';
import MaintenancePage from 'src/pages/error/maintenance.page';

export const RouterConfig = [
  { path: '/auth/login', element: <LoginPage /> },
  { path: '/plan', element: <PlanPage /> },
  { path: '/workflow/:id', element: <WorkflowPage /> },
  { path: '/checkout', element: <CheckoutPage /> },
  { path: '/auth/login', element: <LoginPage /> },
  { path: '/auth/register', element: <RegisterPage /> },
  { path: '/settings', element: <SettingsPage /> },
  { path: '/account', element: <AccountPage /> },
  { path: '/account/activity-log', element: <ActivityLogPage /> },
  { path: '/profile', element: <ProfilePage /> },
  { path: '/oauth', element: <AuthTokenPage /> },
  { path: '/home', element: <HomePage /> },
  { path: '/dashboard', element: <DashboardPage /> },
  { path: '/data-table-example', element: <DataTableExamplePage /> },
  {
    path: '/notifications',
    element: <NotificationsPage />,
    //requiresAccess: false
  },

  // Legal pages
  { path: '/legal/terms', element: <TermsPage /> },
  { path: '/legal/privacy', element: <PrivacyPage /> },

  // Error pages
  { path: '/404', element: <NotFoundPage /> },
  { path: '/500', element: <ServerErrorPage /> },
  { path: '/maintenance', element: <MaintenancePage /> },

  // Setup wizard
  { path: '/setup', element: <SetupWizardPage /> },

  // Onboarding
  { path: '/onboarding', element: <OnboardingPage /> },
];
