
import i18n from 'src/utils/locale';
import { PanelIcon, EditIcon, ProfileIcon } from '@app-studio/web';

export const HOME_CONFIG = {
  interactionMode: "chatbot",
  cards: [
  {
    "title": i18n.t('homeRoutes.browseBlogPosts'),
    "path": "/posts",
    "icon": PanelIcon,
    "actionType": "navigation"
  },
  {
    "title": i18n.t('homeRoutes.createNewPost'),
    "path": "/posts/create",
    "icon": EditIcon,
    "actionType": "navigation"
  },
  {
    "title": i18n.t('homeRoutes.chatWithAi'),
    "path": "/chatbot",
    "icon": ProfileIcon,
    "actionType": "navigation"
  }
],
};
