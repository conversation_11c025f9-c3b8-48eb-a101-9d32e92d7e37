import React from 'react';
import { View, Horizontal, Vertical, Center } from 'app-studio';
import { Loader, Text } from '@app-studio/web';
import 'src/forms';
import i18n from 'src/utils/locale';
import ReactMarkdown from 'react-markdown';

export const PostHeader = (
    {
        requests: requests,
        ...props
    }
) => {
    return !requests.workflowControllerRead.data && requests.workflowControllerRead.isLoading ? <Loader /> : <Vertical alignItems='center' gap={25}><PostTitle data={requests?.workflowControllerRead?.data} {...props} /><AuthorAndDate data={requests?.workflowControllerRead?.data} {...props} /></Vertical>;
};

export const PostContentDisplay = (
    {
        requests: requests,
        ...props
    }
) => {
    return !requests.workflowControllerRead.data && requests.workflowControllerRead.isLoading ? <Loader /> : <Vertical alignItems='center' gap={25}><PostSummary data={requests?.workflowControllerRead?.data} {...props} /><FullContent data={requests?.workflowControllerRead?.data} {...props} /></Vertical>;
};

export const PostTitle = (
    {
        data: data,
        ...props
    }
) => {
    return <Text>{data?.title}</Text>;
};

export const AuthorAndDate = (
    {
        data: data,
        ...props
    }
) => {
    return <Horizontal alignItems='center' className='' gap={25}><Text>{data?.user?.name}</Text><Text>{data?.createdAt}</Text></Horizontal>;
};

export const PostSummary = (
    {
        data: data,
        ...props
    }
) => {
    return <Text>{data?.summary}</Text>;
};

export const FullContent = (
    {
        data: data,
        ...props
    }
) => {
    return (
        <ReactMarkdown
            components={{
                ul: (
                    {
                        children: children
                    }
                ) => <ul
                    style={{
                        paddingLeft: 20,
                        marginBottom: 8
                    }}>{children}</ul>,

                ol: (
                    {
                        children: children
                    }
                ) => <ol
                    style={{
                        paddingLeft: 20,
                        marginBottom: 8
                    }}>{children}</ol>,

                li: (
                    {
                        children: children
                    }
                ) => <li
                    style={{
                        marginBottom: 4,
                        listStyleType: 'disc'
                    }}>{children}</li>
            }}>{data?.content}</ReactMarkdown>
    );
};