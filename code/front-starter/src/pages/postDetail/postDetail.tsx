import React from 'react';
import { Vertical, useMount } from 'app-studio';
import { showModal, Loader } from '@app-studio/web';
import 'src/components';
import * as AsyncStorage from 'src/utils/localstorage';
import { useAuthStore } from 'src/stores/AuthStore';
import { PostHeader, PostContentDisplay } from './postDetail.elements';
import { usePostDetailRequests } from './postDetail.requests';
import { useParams } from 'react-router-dom';

export default function PostDetailPage() {
    const { workflowControllerRead }: any = usePostDetailRequests();

    const {
        id: objectId
    } = useParams();

    const eventProps = {};

    const requests = {
        workflowControllerRead: workflowControllerRead
    };

    useMount(() => {
        const redirectData = AsyncStorage.read('@redirectData');

        if (Object.keys(requests) && redirectData && redirectData.name && (redirectData.params && requests[redirectData.name])) {
            requests[redirectData.name].run(...redirectData.params);
            AsyncStorage.remove('@redirectData');
        }

        workflowControllerRead.run(objectId);
    });

    return <Vertical height='100vh' alignItems='center' gap='50px'><PostHeader requests={requests} {...eventProps} /><PostContentDisplay requests={requests} {...eventProps} /></Vertical>;
}