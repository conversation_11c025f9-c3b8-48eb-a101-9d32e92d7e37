import React from 'react';
import { Vertical, useMount } from 'app-studio';
import { showModal, Loader } from '@app-studio/web';
import 'src/components';
import * as AsyncStorage from 'src/utils/localstorage';
import { useAuthStore } from 'src/stores/AuthStore';
import { FeedHeader, BlogPostsList } from './blogPostsFeed.elements';
import { useBlogPostsFeedRequests } from './blogPostsFeed.requests';
import { useNavigate } from 'react-router-dom';

export default function BlogPostsFeedPage() {
    const navigate = useNavigate();
    const { workflowControllerFind }: any = useBlogPostsFeedRequests();

    const eventProps = {
        post_entryClick: item => navigate(`/workflow/${item.id}`)
    };

    const requests = {
        workflowControllerFind: workflowControllerFind
    };

    useMount(() => {
        const redirectData = AsyncStorage.read('@redirectData');

        if (Object.keys(requests) && redirectData && redirectData.name && (redirectData.params && requests[redirectData.name])) {
            requests[redirectData.name].run(...redirectData.params);
            AsyncStorage.remove('@redirectData');
        }

        workflowControllerFind.run({ownerType: 'post'});
    });

    return <Vertical height='100vh' alignItems='center' gap='50px'><FeedHeader requests={requests} {...eventProps} /><BlogPostsList requests={requests} {...eventProps} /></Vertical>;
}