import React from 'react';
import { View, Horizontal, Vertical, Center } from 'app-studio';
import { Loader, Text, Badge } from '@app-studio/web';
import 'src/forms';
import i18n from 'src/utils/locale';

export const FeedHeader = (
    {
        requests: requests,
        ...props
    }
) => {
    return <Vertical alignItems='center' gap={25}><ScreenTitle {...props} /></Vertical>;
};

export const BlogPostsList = (
    {
        requests: requests,
        ...props
    }
) => {
    return !requests.workflowControllerFind.data && requests.workflowControllerFind.isLoading ? <Loader /> : <Vertical alignItems='center' gap={25}>{requests?.workflowControllerFind?.data?.length > 0 && requests?.workflowControllerFind?.data.map(item => <PostFeed key={item.id} item={item} {...props} />)}</Vertical>;
};

export const ScreenTitle = (
    {
        ...props
    }
) => {
    return <Text>{i18n.t('BlogPostsFeed.ScreenTitle.undefined.textId')}</Text>;
};

export const PostFeed = (
    {
        item: item,
        ...props
    }
) => {
    return (
        <Horizontal alignItems='center' className='post_entry' gap={25}><Text className='post_entry' onClick={() => props.post_entryClick(item)}>{item.tasks?.[item.tasks.length - 1]?.result?.input?.title}</Text><Text className='post_entry' onClick={() => props.post_entryClick(item)}>{item.tasks?.[item.tasks.length - 1]?.result?.input?.summary}</Text><Text className='post_entry' onClick={() => props.post_entryClick(item)}>{item.tasks?.[item.tasks.length - 1]?.result?.input?.user.name}</Text><Badge
                content={''}
                className='post_entry'
                onClick={() => props.post_entryClick(item)}></Badge></Horizontal>
    );
};