
import { StorySpec } from 'specs/specs.interface';

export const postToRead: StorySpec = {
  filePath: __filename,
  route: '/workflow/post/:id',
  method: 'GET',
  operation: {
    summary: 'As a user, I want to read a post',
  },
  access: {
    resource: 'post',
    action: 'read',
    owner: false
  },
  codes: {
        '200': {
          response: {
            status: 200,
            description: "post's data",
          },
          story: {
        auth: "user",
        path: {"id":"seed.postToRead.result.id"}
      },
          tests: [],
        },
        '401': {
          response: {
            status: 401,
            description: 'Incorrect credentials',
          },
        },
        '404': {
          response: {
            status: 404,
            description: "post doesn't exist",
          },
        },
      },
};
