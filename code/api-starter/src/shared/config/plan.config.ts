
      export type PaymentType = 'subscription' | 'one-time-payment' | 'credit' | 'cart';
      export const PAYMENT_TYPES: PaymentType[] = ['subscription', 'one-time-payment', 'credit', 'cart'];
      export const PLAN_CONFIG = {
  "paymentType": "credit" as PaymentType,
  "paymentEntity": "post",
  "options": {
    "creditPackSmall": {
      "id": "creditPackSmall",
      "name": "Small Credit Pack",
      "description": "Purchase 50 credits to unlock approximately 5 exclusive posts.",
      "price": 5,
      "currency": "usd",
      "interval": null,
      "credits": 50,
      "buttonLabel": "Buy 50 Credits"
    },
    "creditPackMedium": {
      "id": "creditPackMedium",
      "name": "Medium Credit Pack",
      "description": "Purchase 100 credits to unlock approximately 10 exclusive posts.",
      "price": 9,
      "currency": "usd",
      "interval": null,
      "credits": 100,
      "buttonLabel": "Buy 100 Credits"
    },
    "creditPackLarge": {
      "id": "creditPackLarge",
      "name": "Large Credit Pack",
      "description": "Purchase 250 credits to unlock approximately 25 exclusive posts.",
      "price": 20,
      "currency": "usd",
      "interval": null,
      "credits": 250,
      "buttonLabel": "Buy 250 Credits"
    }
  },
  "creditNeeded": 10,
  "currency": "usd"
};
    