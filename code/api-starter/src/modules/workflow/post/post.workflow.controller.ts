
        import {
          Body,
          Controller,
          Delete as NestDelete,
          Get as NestGet,
          NotFoundException,
          Param,
          Patch as NestPatch,
          Post as NestPost,
          UseGuards,
          UseInterceptors,
          UploadedFile,
        } from '@nestjs/common';
        import {
          ApiBearerAuth,
          ApiOperation,
          ApiResponse,
          ApiConsumes,
          ApiTags,
        } from '@nestjs/swagger';
        import { AccessTo, AccessCreditNeeded} from 'src/acl/acl.decorator';
        import { AccessGuard } from 'src/acl/acl.guard';
        import { Post, Prisma, User } from '@prisma/client';
        import { AuthGuard } from 'src/modules/auth/auth.guard';
        import { ReqUser } from 'src/modules/auth/auth.decorator';
        import * as story from 'specs/story/post';
        import {ApiFile} from 'src/shared/swagger/swagger.decorator';
        import {FileInterceptor} from '@nestjs/platform-express';
        import {Upload} from 'src/shared/file/file.interface';

        import {
  FindPostParams,
} from './post.workflow.swagger';

        
        
        import { PostWorkflowService } from './post.workflow.service';
        import { WorkflowService } from '../workflow.service';
              

        @Controller("workflow/post" )
        @ApiTags("workflow")
        @ApiBearerAuth()
        export class PostWorkflowController {
          constructor(
            
            
        private postWorkflowService: PostWorkflowService,
        private workflowService: WorkflowService
      
          ) {}

          
    
    
      whereToPrisma(params): Prisma.PostWhereInput {
        let where: Prisma.PostWhereInput = {};
        const OR = [];
        
        if (OR.length > 0) {
          where.OR = OR;
        }
        return where;
      }

      @NestPost('find')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.postToFind.access)
      @ApiOperation(story.postToFind.operation)
      @ApiResponse(story.postToFind.codes['201'].response)
      async find(@ReqUser() user: User,@Body() params: FindPostParams): Promise<Post[]> {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;

        const currentData = await this.postWorkflowService.find({
          where: this.whereToPrisma(where),
          orderBy: { [sortField]: sortOrder },
          take,
          skip,
        });

        if(currentData && currentData.length > 0){
        
        }
        return currentData;
      }

    
    
    

    
    
    
        @NestGet(':id')
        @UseGuards(AuthGuard, AccessGuard)
        @AccessTo(story.postToRead.access)
        @ApiOperation(story.postToRead.operation)
        @ApiResponse(story.postToRead.codes['200'].response)
        @ApiResponse(story.postToRead.codes['401'].response)
        @ApiResponse(story.postToRead.codes['404'].response)
        async read(@ReqUser() user: User, @Param('id') id: string): Promise<Post> {
          const currentData = await this.postWorkflowService.get({ id });
          if (!currentData) {
            throw new NotFoundException();
          }
          
          return currentData;
        }

    
    

        }
