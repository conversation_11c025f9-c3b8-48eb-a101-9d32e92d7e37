import { PrismaService } from 'src/shared/prisma/prisma.service';
import { Prisma, Workflow, WorkflowStatus, Task, OwnerType } from '@prisma/client';
import { WorkerService } from 'src/modules/workflow/worker.service';
export declare class WorkflowService {
    prisma: PrismaService;
    worker: WorkerService;
    private kc;
    private k8sApi;
    maxWorker: number;
    maxRetries: number;
    namespace: string;
    constructor(prisma: PrismaService, worker: WorkerService);
    get(where: Prisma.WorkflowWhereUniqueInput): Promise<Workflow>;
    create(userId: string, projectId: string, name: string, inputs: any, steps: any[], options: {
        ownerId: string;
        ownerType: OwnerType;
        actionType: string;
        objectName: string;
        stepId?: string;
    }): Promise<{
        workflowId: string;
        tasks: Task[];
    }>;
    find({ ownerId, ownerType, }: Prisma.WorkflowWhereInput): Promise<Workflow[]>;
    private initiateTasks;
    createAndExecuteTask(workflow: Workflow, step: any, inputs: any): Promise<{
        error: Prisma.JsonValue | null;
        result: Prisma.JsonValue | null;
        id: string;
        status: import(".prisma/client").$Enums.WorkflowStatus;
        duration: number;
        nbOfExecution: number;
        taskName: string;
        next: Prisma.JsonValue | null;
        dependencies: Prisma.JsonValue | null;
        ownerId: string;
        ownerType: import(".prisma/client").$Enums.OwnerType;
        skill: import(".prisma/client").$Enums.Skill;
        inputs: Prisma.JsonValue;
        podId: string | null;
        workflowId: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    requestTask({ taskName, podId, }: {
        taskName: string;
        podId: string;
    }): Promise<Task | null>;
    updateTaskStatus(taskId: string, status: WorkflowStatus, result?: string, error?: any): Promise<Task>;
    private incrementTaskExecutionCount;
    private handleWorkflowStatusChange;
    getInputs(workflow: Workflow, step: any, doneTasksObject: Record<string, Task>): Promise<any>;
    startPendingWorkflows(): Promise<void>;
    continueWorkflow(workflow: Workflow): Promise<void>;
    extractNames(obj: any): any[];
    resetTask(taskId: string, resetNbOfExecution?: boolean): Promise<Task | null>;
    private initializeWorkflowDirectory;
    getTask(taskId: any): Promise<Task>;
    getTasks(workflowId: any): Promise<Task[]>;
    private executeOrDeployWorker;
    deployWorker(taskName: string, namespace: string, env: string): Promise<void>;
}
