"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostWorkflowService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../../shared/prisma/prisma.service");
const file_service_1 = require("../../../shared/file/file.service");
const ai_service_1 = require("../../../shared/ai/ai.service");
const helpers_1 = require("../../../shared/format/helpers");
const contentGenerationPrompt_1 = require("../../../prompts/contentGenerationPrompt");
let PostWorkflowService = class PostWorkflowService {
    prisma;
    aiService;
    fileService;
    constructor(prisma, aiService, fileService) {
        this.prisma = prisma;
        this.aiService = aiService;
        this.fileService = fileService;
    }
    async create(data) {
        return await this.prisma.post.create({
            data,
        });
    }
    async get(where) {
        return await this.prisma.post.findUnique({ where });
    }
    async delete(where) {
        try {
            return await this.prisma.post.delete({ where });
        }
        catch (e) {
            return false;
        }
    }
    async update(where, params) {
        return await this.prisma.post.update({ data: params, where });
    }
    async findAll() {
        return await this.prisma.post.findMany();
    }
    async find({ where, orderBy, skip = 0, take = 10, }) {
        return await this.prisma.post.findMany({ where, orderBy, skip, take });
    }
    async count(where) {
        return await this.prisma.post.count({ where });
    }
    async generatePostFromPromptInput(inputs, taskId, workflowId) {
        let response = await this.aiService.send({
            user: inputs.user,
            type: 'text',
            messages: [
                {
                    role: 'user',
                    content: (0, contentGenerationPrompt_1.contentGenerationPrompt)(inputs.input.topic, 'post'),
                },
            ],
        });
        return { input: (0, helpers_1.extractJson)(response) };
    }
    async storePostFromPromptInput(inputs, taskId, workflowId) {
        const output = await this.create({
            userId: inputs.user.id,
            workflowId,
            topic: inputs.input.topic,
            ...inputs['postWorkflowService.generatePostFromPromptInput'].input,
        });
        return { input: output };
    }
};
exports.PostWorkflowService = PostWorkflowService;
exports.PostWorkflowService = PostWorkflowService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        ai_service_1.AiService,
        file_service_1.FileService])
], PostWorkflowService);
//# sourceMappingURL=post.workflow.service.js.map