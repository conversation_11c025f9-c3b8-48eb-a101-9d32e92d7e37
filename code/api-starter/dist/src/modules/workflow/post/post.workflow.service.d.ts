import { Post, Prisma } from '@prisma/client';
import { PrismaService } from 'src/shared/prisma/prisma.service';
import { FileService } from 'src/shared/file/file.service';
import { AiService } from 'src/shared/ai/ai.service';
export declare class PostWorkflowService {
    prisma: PrismaService;
    aiService: AiService;
    fileService: FileService;
    constructor(prisma: PrismaService, aiService: AiService, fileService: FileService);
    create(data: Prisma.PostCreateInput): Promise<Post>;
    get(where: Prisma.PostWhereUniqueInput): Promise<Post>;
    delete(where: Prisma.PostWhereUniqueInput): Promise<any>;
    update(where: Prisma.PostWhereUniqueInput, params: Prisma.PostUpdateInput): Promise<Post>;
    findAll(): Promise<Post[]>;
    find({ where, orderBy, skip, take, }: Prisma.PostFindManyArgs): Promise<Post[]>;
    count(where: Prisma.PostWhereInput): Promise<number>;
    generatePostFromPromptInput(inputs: any, taskId: any, workflowId: any): Promise<any>;
    storePostFromPromptInput(inputs: any, taskId: any, workflowId: any): Promise<any>;
}
