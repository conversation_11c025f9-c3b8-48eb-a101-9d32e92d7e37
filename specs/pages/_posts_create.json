{"screenSpecs": {"name": "CreatePostScreen", "type": "page", "path": "/posts/create", "description": "Screen for creating new posts, offering both manual input via a rich text editor and AI-driven content generation based on a given topic or keywords.", "userExperience": [{"who": "App", "action": {"type": "load"}, "where": "/posts/create"}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "post_title_input"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "fill", "element": {"type": "textarea", "eventId": "post_summary_input"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "type", "element": {"type": "textarea", "eventId": "post_content_editor"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "click", "element": {"type": "toggle", "eventId": "is_exclusive_toggle"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "price_credits_input"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "publish_post_manual"}}, "where": "/posts/create"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createPostManual_request_1", "notify": {"target": "me", "title": "Post Created", "message": "Your post has been successfully created and is being processed.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "publish_post_manual"}}, "where": "/posts/create"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPostManual_request_1"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_ai_post_button"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "ai_topic_input"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_ai_content"}}, "where": "/posts/create"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createPostAI_request_2", "notify": {"target": "me", "title": "AI Post Generation Initiated", "message": "Your AI-generated post is being created and will be available shortly.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_ai_content"}}, "where": "/posts/create"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPostAI_request_2"}}, "where": "/posts/create"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user's post, containing content, status, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who owns this post, established by the userId relation."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional ID of the associated workflow."}, "isExclusive": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is exclusive."}, "priceCredits": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post."}, "status": {"type": "String", "required": true, "isUser": false, "description": "The current status of the post (e.g., 'draft', 'published')."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is currently accessible."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was last updated."}}}}, "requests": {"createPostManual_request_1": {"useWorkflow": {"tasks": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your manually created post.", "dependencies": [], "next": []}]}, "useAi": null, "type": "POST", "params": {}, "body": {"title": "string", "summary": "string", "content": "string", "isExclusive": "boolean", "priceCredits": "int", "status": "draft"}, "notifyLink": "/posts/:id", "onSuccess": {"actionType": "navigate", "path": "/workflow/:id"}}, "createPostAI_request_2": {"useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating post content using AI.", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your AI-generated post.", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "Content Generator", "objective": "Generate a comprehensive and engaging blog post based on the provided topic.", "context": ["topic"], "output": [{"title": "title", "outputDescription": "The main title of the generated post."}, {"summary": "summary", "outputDescription": "A brief summary of the post content."}, {"content": "content", "outputDescription": "The main body of the generated post."}, {"isExclusive": "isExclusive", "outputDescription": "Indicates if the post is exclusive."}, {"priceCredits": "priceCredits", "outputDescription": "The number of credits required to access the post."}, {"status": "status", "outputDescription": "The current status of the post (e.g., 'draft')."}, {"topic": "topic", "outputDescription": "The topic or category of the post used for generation."}]}, "type": "POST", "params": {}, "body": {"topic": "string"}, "notifyLink": "/posts/:id", "accessCredit": {"consumes": {"text": 1}}, "onSuccess": {"actionType": "navigate", "path": "/workflow/:id"}}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "PostCreationHeader", "layoutType": "Horizontal", "description": "Header section with title and option to trigger AI generation.", "elements": [{"name": "Page<PERSON><PERSON>le", "description": "Title of the post creation page.", "components": [{"component": "Text", "texts": {"textId": "page_title", "text": "Create New Post"}}]}, {"name": "GenerateAIButton", "description": "Button to initiate AI content generation.", "components": [{"eventId": "generate_ai_post_button", "component": "<PERSON><PERSON>", "texts": {"buttonText": "Generate with AI"}}]}]}, {"groupName": "ManualPostInputForm", "layoutType": "Vertical", "description": "Form for manual post content input.", "elements": [{"name": "ManualPostForm", "description": "Form for entering post details manually.", "inputs": [{"eventId": "post_title_input", "componentName": "TextField", "fieldName": "title"}, {"eventId": "post_summary_input", "componentName": "TextArea", "fieldName": "summary"}, {"eventId": "post_content_editor", "componentName": "TextArea", "fieldName": "content"}, {"eventId": "is_exclusive_toggle", "componentName": "Toggle", "fieldName": "isExclusive"}, {"eventId": "price_credits_input", "componentName": "TextField", "fieldName": "priceCredits"}, {"eventId": "publish_post_manual", "componentName": "<PERSON><PERSON>", "type": "submit"}]}]}, {"groupName": "AICreationSection", "layoutType": "Vertical", "description": "Section for AI-driven post content generation.", "elements": [{"name": "AIPromptInput", "description": "Input field for AI topic/keywords.", "inputs": [{"eventId": "ai_topic_input", "componentName": "TextField", "fieldName": "topic"}, {"eventId": "generate_ai_content", "componentName": "<PERSON><PERSON>", "type": "submit"}]}]}]}]}}