{"screenSpecs": {"name": "BlogPostsFeed", "type": "page", "path": "/posts", "description": "Displays a scrollable feed of blog posts, showing titles, authors, summaries, and indicating premium 'Exclusive' content. Allows users to discover content.", "userExperience": [{"who": "App", "action": {"type": "load", "request": {"requestId": "listPosts_request_1"}}, "where": "/posts"}, {"who": "User", "action": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user's post, containing content, status, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who owns this post, established by the userId relation."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional ID of the associated workflow."}, "isExclusive": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is exclusive."}, "priceCredits": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post."}, "status": {"type": "String", "required": true, "isUser": false, "description": "The current status of the post (e.g., 'draft', 'published')."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is currently accessible."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was last updated."}}}}, "requests": {"listPosts_request_1": {"isArray": true, "dataResult": {"items": [{"id": "post123", "title": "Understanding Modern UI Frameworks", "summary": "An in-depth look at popular UI frameworks like React and Vue.", "user": {"name": "Alice Wonderland"}, "isExclusive": false}, {"id": "post124", "title": "The Future of AI in Content Generation", "summary": "Exploring advanced AI models and their impact on creative industries.", "user": {"name": "<PERSON> Builder"}, "isExclusive": true}]}, "type": "POST", "path": "/workflow/post/find"}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layoutType": "Vertical", "description": "Contains the main title for the blog post feed.", "elements": [{"name": "ScreenTitle", "description": "Title of the blog feed screen", "components": [{"component": "Text", "texts": {"textId": "feedTitle", "text": "Blog Posts"}}]}]}, {"groupName": "BlogPostsList", "layoutType": "Vertical", "description": "Displays a scrollable list of blog post summaries.", "elements": [{"name": "PostFeed", "description": "A list of all blog posts, each with a summary, author, and exclusivity status.", "dataSource": "listPosts_request_1", "items": true, "dataRequest": {"type": "POST", "path": "/workflow/post/find", "fields": {"PostFeed": "items"}}, "components": [{"eventId": "post_entry", "component": "Text", "data": "title"}, {"eventId": "post_entry", "component": "Text", "data": "summary"}, {"eventId": "post_entry", "component": "Text", "data": "user.name"}, {"eventId": "post_entry", "component": "Badge", "data": "isExclusive", "texts": {"textId": "exclusiveLabel", "text": "Exclusive"}}]}]}]}]}}