{"screenSpecs": {"name": "PostDetail", "type": "page", "path": "/posts/:id", "description": "Dedicated screen for viewing the full content of a selected blog post, with conditional access to exclusive content via a paywall modal for a one-time credit purchase.", "userExperience": [{"who": "App", "action": {"type": "load", "request": {"requestId": "readPost_request_2", "notify": null}}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"isExclusive": true, "hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "when": {"type": "load", "request": "readPost_request_2"}, "where": "/posts/:id"}], "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user's post, containing content, status, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who owns this post, established by the userId relation."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional ID of the associated workflow."}, "isExclusive": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is exclusive."}, "priceCredits": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post."}, "status": {"type": "String", "required": true, "isUser": false, "description": "The current status of the post (e.g., 'draft', 'published')."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is currently accessible."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was last updated."}}}}, "requests": {"readPost_request_2": {"isArray": false, "dataResult": {"id": "post_id_123", "title": "My First Exclusive Blog Post", "summary": "This is a brief summary of my first exclusive blog post, designed to entice readers.", "content": "## Introduction\nThis is the full, detailed content of my exclusive blog post. It covers advanced topics...\n\n### Section 1\nDetails about section one...\n\n### Conclusion\nSummarizing the key takeaways.", "userId": "user_abc", "user": {"name": "<PERSON>"}, "workflowId": null, "isExclusive": true, "priceCredits": 10, "status": "published", "hasAccess": false, "topic": "Technology", "createdAt": "2023-10-27T10:00:00Z", "updatedAt": "2023-10-27T10:30:00Z"}, "type": "GET", "params": {"id": "string"}, "path": "/workflow/post/:id"}}, "layouts": [{"layoutType": "Vertical", "groups": [{"groupName": "PostH<PERSON><PERSON>", "layoutType": "Vertical", "description": "Displays the post title, author, and creation date.", "elements": [{"name": "PostTitle", "description": "Main title of the blog post.", "dataSource": "readPost_request_2", "components": [{"component": "Text", "data": "title"}]}, {"name": "AuthorAndDate", "description": "Displays the author's name and post creation date.", "dataSource": "readPost_request_2", "components": [{"component": "Text", "data": "user.name"}, {"component": "Text", "data": "createdAt"}]}]}, {"groupName": "PostContentDisplay", "layoutType": "Vertical", "description": "Displays the summary and full content of the blog post.", "elements": [{"name": "PostSummary", "description": "A brief summary of the post.", "dataSource": "readPost_request_2", "components": [{"component": "Text", "data": "summary"}]}, {"name": "FullContent", "description": "The main content of the post, formatted with markdown.", "dataSource": "readPost_request_2", "components": [{"component": "ReactMarkdown", "data": "content"}]}]}]}]}}