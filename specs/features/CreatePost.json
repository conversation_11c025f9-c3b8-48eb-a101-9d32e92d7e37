{"feature": {"name": "Create Post", "description": "Allows users to create new blog posts, either by writing content manually using a rich text editor or by leveraging AI to generate post content based on a user-provided topic prompt.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load"}, "where": "/posts/create"}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "post_title_input"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "fill", "element": {"type": "textarea", "eventId": "post_content_editor"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "publish_post_manual"}}, "where": "/posts/create"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createPostManual_request_1", "notify": {"target": "me", "title": "Post Created", "message": "Your post has been successfully created and is being processed.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "publish_post_manual"}}, "where": "/posts/create"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPostManual_request_1"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_ai_post_button"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "ai_topic_input"}}, "where": "/posts/create"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_ai_content"}}, "where": "/posts/create"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createPostAI_request_2", "notify": {"target": "me", "title": "AI Post Generation Initiated", "message": "Your AI-generated post is being created and will be available shortly.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_ai_content"}}, "where": "/posts/create"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPostAI_request_2"}}, "where": "/posts/create"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readWorkflow_request_3"}}, "where": "/workflow/:id"}, {"who": "App", "action": {"type": "navigate", "path": "/posts/:id"}, "when": {"type": "load", "request": {"requestId": "readWorkflow_request_3"}}, "where": "/workflow/:id"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPost_request_4"}}, "where": "/posts/:id"}], "screens": {"/posts/create": "A screen featuring a rich text editor for manual content input and an integrated option to prompt AI for content generation based on a given topic or keywords. This is the main screen for post creation.", "/workflow/:id": "A screen to display the progress of the post generation workflow, whether manual or AI-assisted, until completion.", "/posts/:id": "A screen to display the detailed content of a single blog post after it has been created and processed by the workflow."}, "conditions": ["User is authenticated to create posts.", "User has access to the post creation feature.", "Manual content input allows for rich text editing.", "AI content generation requires a user-provided topic or prompt."], "dataSchemas": {"post": {"canBeCommented": false, "usePayment": false, "description": "Represents a blog post, either created manually or with AI assistance.", "fields": {"id": {"type": "string", "required": true, "isUser": false, "description": "Unique identifier for the blog post."}, "title": {"type": "string", "required": true, "isUser": false, "description": "The title of the blog post."}, "content": {"type": "string", "required": true, "isUser": false, "description": "The main body content of the blog post."}, "authorId": {"type": "string", "required": true, "isUser": true, "description": "The ID of the user who authored the post."}, "status": {"type": "string", "required": true, "isUser": false, "description": "The current status of the post (e.g., 'draft', 'published', 'processing')."}, "workflowId": {"type": "string", "required": false, "isUser": false, "description": "Workflow identifier for the post generation process."}}}, "workflow": {"canBeCommented": false, "usePayment": false, "description": "Represents the status and details of a background workflow process for resource generation or modification.", "fields": {"id": {"type": "string", "required": true, "isUser": false, "description": "Unique identifier for the workflow."}, "status": {"type": "string", "required": true, "isUser": false, "description": "Current status of the workflow (e.g., 'pending', 'processing', 'completed', 'failed')."}, "resourceId": {"type": "string", "required": false, "isUser": false, "description": "ID of the resource being generated or modified by the workflow."}, "resourceType": {"type": "string", "required": false, "isUser": false, "description": "Type of resource being generated or modified (e.g., 'post', 'image')."}}}}, "requests": [{"requestId": "createPostManual_request_1", "useWorkflow": {"tasks": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your manually created post.", "dependencies": [], "next": []}]}, "useAi": null, "dataSchema": "post", "type": "Create", "params": {}, "body": {"title": "string", "content": "string", "status": "string"}, "notifyLink": "/posts/:id"}, {"requestId": "createPostAI_request_2", "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating post content using AI.", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your AI-generated post.", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "Content Generator", "objective": "Generate a comprehensive and engaging blog post based on the provided topic."}, "dataSchema": "post", "type": "Create", "params": {}, "body": {"topic": "string"}, "notifyLink": "/posts/:id"}, {"requestId": "readWorkflow_request_3", "useWorkflow": null, "useAi": null, "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}, "body": {}, "notifyLink": null}, {"requestId": "readPost_request_4", "useWorkflow": null, "useAi": null, "dataSchema": "post", "type": "Read", "params": {"id": "string"}, "body": {}, "notifyLink": null}]}}