{"feature": {"name": "Read Post", "description": "Enables users to view the full content of a selected blog post. For exclusive posts, access to the full content is restricted and requires a one-time purchase using credits.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPosts_request_1"}}, "where": "/posts"}, {"who": "User", "action": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readWorkflow_request_1"}}, "where": "/workflow/:id"}, {"who": "App", "action": {"type": "navigate", "path": "/posts/:id"}, "when": {"type": "load", "request": {"requestId": "readWorkflow_request_1"}}, "where": "/workflow/:id"}, {"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_2"}}, "where": "/posts/:id"}], "screens": {"/posts": "Main screen displaying a list of blog posts. Presents summaries for all posts, with exclusive posts indicated as premium content.", "/workflow/:id": "Screen to check the generation status of a blog post before full content display.", "/posts/:id": "Dedicated screen for viewing the full content of a selected blog post. Access to exclusive content requires a one-time purchase using credits, managed via a paywall modal."}, "conditions": ["User can view a list of posts.", "Exclusive posts are clearly marked on the list.", "Full content of exclusive posts requires a one-time credit purchase.", "Purchased exclusive posts become fully accessible to the user."], "dataSchemas": {"post": {"canBeCommented": true, "usePayment": true, "description": "Entity for detailed information about a blog post.", "fields": {"id": {"type": "string", "required": true, "isUser": false, "description": "Unique identifier of the post."}, "title": {"type": "string", "required": true, "isUser": false, "description": "Title of the blog post."}, "content": {"type": "string", "required": true, "isUser": false, "description": "Full content of the blog post."}, "author": {"type": "string", "required": true, "isUser": true, "description": "Name of the post's author."}, "isExclusive": {"type": "boolean", "required": true, "isUser": false, "description": "Indicates if the post requires purchase for access."}, "priceCredits": {"type": "number", "required": true, "isUser": false, "description": "Cost in credits to unlock the exclusive post."}, "workflowId": {"type": "string", "required": false, "isUser": false, "description": "Workflow identifier for the post."}}}, "workflow": {"canBeCommented": false, "usePayment": false, "description": "Entity representing a workflow process for resource generation.", "fields": {"id": {"type": "string", "required": true, "isUser": false, "description": "Unique identifier for the workflow process."}, "status": {"type": "string", "required": true, "isUser": false, "description": "Current status of the workflow (e.g., 'pending', 'generating', 'completed', 'failed')."}, "progress": {"type": "number", "required": true, "isUser": false, "description": "Percentage of completion for the workflow."}}}}, "requests": [{"requestId": "readPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}, {"requestId": "readWorkflow_request_1", "isArray": false, "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}}, {"requestId": "readPost_request_2", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}}]}}