{"feature": {"name": "List Posts", "description": "Displays a dynamic collection of all available blog posts in a feed. Each entry shows the post title and author, with a clear 'Exclusive' label for content that requires credits to unlock. Only basic information is visible for exclusive posts.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "listPosts_request_1"}}, "where": "/posts"}, {"who": "User", "action": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "action": {"type": "load", "request": {"requestId": "readWorkflow_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "action": {"type": "navigate", "path": "/posts/:id"}, "where": "/workflow/:id"}, {"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}, "where": "/posts/:id"}], "screens": {"/posts": "The main screen displaying a scrollable feed of blog posts, including titles, authors, and 'Exclusive' labels for premium content. Allows users to discover content.", "/workflow/:id": "A screen to check the processing or generation status of a post before it is displayed.", "/posts/:id": "The screen dedicated to displaying the full content of a single blog post, or a paywall if access is restricted."}, "conditions": ["Posts are displayed in a comprehensive list format.", "Each post entry clearly shows its title and author.", "Exclusive posts are explicitly labeled to differentiate them from free content.", "Only basic information is visible for exclusive posts in the listing view.", "Access to full exclusive post content requires credits."], "dataSchemas": {"post": {"canBeCommented": false, "usePayment": true, "description": "Entity for detailed information about a blog post, including its content and exclusivity status.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the post."}, "title": {"type": "string", "required": true, "description": "Title of the blog post."}, "author": {"type": "string", "required": true, "isUser": true, "description": "Author of the blog post."}, "content": {"type": "string", "required": true, "description": "Full content of the blog post, visible upon access."}, "isExclusive": {"type": "boolean", "required": true, "description": "Indicates if the post requires credits or payment to unlock."}, "workflowId": {"type": "string", "required": false, "description": "Workflow identifier for the post generation or processing."}}}, "workflow": {"canBeCommented": false, "usePayment": false, "description": "Entity for tracking the status of resource generation or processing workflows.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the workflow."}, "status": {"type": "string", "required": true, "description": "Current status of the workflow (e.g., 'pending', 'completed', 'failed')."}, "resourceId": {"type": "string", "required": true, "description": "ID of the resource being processed by the workflow."}, "resourceType": {"type": "string", "required": true, "description": "Type of the resource being processed (e.g., 'post', 'image')."}}}}, "requests": [{"requestId": "listPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find", "params": {}, "body": {}}, {"requestId": "readWorkflow_request_2", "isArray": false, "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}}, {"requestId": "readPost_request_3", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}}]}}