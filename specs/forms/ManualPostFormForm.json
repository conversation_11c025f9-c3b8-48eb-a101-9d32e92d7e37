{"form": {"name": "ManualPostForm", "description": "Form for entering post details manually.", "inputs": [{"eventId": "post_title_input", "data-testid": "post-title-input", "component": "FormikTextField", "fieldName": "title", "type": "string", "label": "Post Title", "placeholder": "Enter the post title", "data": {"value": ""}, "validations": {"required": {"enabled": true, "requiredMessageError": "Post title is required."}, "minLength": {"enabled": true, "value": 5, "invalidMessageError": "Title must be at least 5 characters."}, "maxLength": {"enabled": true, "value": 100, "invalidMessageError": "Title cannot exceed 100 characters."}}}, {"eventId": "post_summary_input", "data-testid": "post-summary-input", "component": "FormikTextArea", "fieldName": "summary", "type": "string", "label": "Post Summary", "placeholder": "Provide a brief summary of the post", "data": {"value": ""}, "validations": {"required": {"enabled": true, "requiredMessageError": "Post summary is required."}, "maxLength": {"enabled": true, "value": 200, "invalidMessageError": "Summary cannot exceed 200 characters."}}}, {"eventId": "post_content_editor", "data-testid": "post-content-editor", "component": "FormikTextArea", "fieldName": "content", "type": "string", "label": "Post Content", "placeholder": "Write the main content of your post", "data": {"value": ""}, "validations": {"required": {"enabled": true, "requiredMessageError": "Post content is required."}, "minLength": {"enabled": true, "value": 50, "invalidMessageError": "Content must be at least 50 characters."}}}, {"eventId": "is_exclusive_toggle", "data-testid": "is-exclusive-toggle", "component": "Formik<PERSON>oggle", "fieldName": "isExclusive", "type": "boolean", "label": "Exclusive Content", "placeholder": "Toggle if this post is exclusive", "data": {"isChecked": false}, "validations": {}}, {"eventId": "price_credits_input", "data-testid": "price-credits-input", "component": "FormikTextField", "fieldName": "priceCredits", "type": "number", "label": "Price (Credits)", "placeholder": "Enter price in credits", "data": {"value": ""}, "validations": {"required": {"enabled": true, "requiredMessageError": "Price in credits is required."}, "min": {"enabled": true, "value": 0, "invalidMessageError": "Price cannot be negative."}, "max": {"enabled": true, "value": 255, "invalidMessageError": "Price cannot exceed 255 credits."}}}], "buttons": [{"data-testid": "publish-post-button", "eventId": "publish_post_manual", "componentName": "<PERSON><PERSON>", "type": "submit", "data": {"text": "Publish"}}]}}