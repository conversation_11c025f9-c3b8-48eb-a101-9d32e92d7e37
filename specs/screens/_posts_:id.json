{"screenSpecs": {"path": "/posts/:id", "description": "A screen to display the detailed content of a single blog post after it has been created and processed by the workflow. The screen dedicated to displaying the full content of a single blog post, or a paywall if access is restricted. Dedicated screen for viewing the full content of a selected blog post. Access to exclusive content requires a one-time purchase using credits, managed via a paywall modal", "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user's post, containing content, status, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who owns this post, established by the userId relation."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional ID of the associated workflow."}, "isExclusive": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is exclusive."}, "priceCredits": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post."}, "status": {"type": "String", "required": true, "isUser": false, "description": "The current status of the post (e.g., 'draft', 'published')."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is currently accessible."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was last updated."}}}}, "userExperience": {"CreatePost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPost_request_4"}}}], "ListPosts": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_3"}}}], "ReadPost": [{"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPost_request_2"}}}]}, "requests": {"readPost_request_4": {"requestId": "readPost_request_4", "useWorkflow": null, "useAi": null, "dataSchema": "post", "type": "Read", "params": {"id": "string"}, "body": {}, "notifyLink": null}, "readPost_request_3": {"requestId": "readPost_request_3", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, "readPost_request_2": {"requestId": "readPost_request_2", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}}}}}