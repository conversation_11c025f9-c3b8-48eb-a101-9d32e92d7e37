{"screenSpecs": {"path": "/posts/create", "description": "A screen featuring a rich text editor for manual content input and an integrated option to prompt AI for content generation based on a given topic or keywords. This is the main screen for post creation", "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user's post, containing content, status, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who owns this post, established by the userId relation."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional ID of the associated workflow."}, "isExclusive": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is exclusive."}, "priceCredits": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post."}, "status": {"type": "String", "required": true, "isUser": false, "description": "The current status of the post (e.g., 'draft', 'published')."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is currently accessible."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was last updated."}}}}, "userExperience": {"CreatePost": [{"who": "App", "if": null, "action": {"type": "load"}}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "post_title_input"}}}, {"who": "User", "action": {"type": "fill", "element": {"type": "textarea", "eventId": "post_content_editor"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "publish_post_manual"}}}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createPostManual_request_1", "notify": {"target": "me", "title": "Post Created", "message": "Your post has been successfully created and is being processed.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "publish_post_manual"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPostManual_request_1"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_ai_post_button"}}}, {"who": "User", "action": {"type": "fill", "element": {"type": "input:text", "eventId": "ai_topic_input"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "generate_ai_content"}}}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createPostAI_request_2", "notify": {"target": "me", "title": "AI Post Generation Initiated", "message": "Your AI-generated post is being created and will be available shortly.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_ai_content"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "send", "request": {"requestId": "createPostAI_request_2"}}}]}, "requests": {"createPostManual_request_1": {"requestId": "createPostManual_request_1", "useWorkflow": {"tasks": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your manually created post.", "dependencies": [], "next": []}]}, "useAi": null, "dataSchema": "post", "type": "Create", "params": {}, "body": {"title": "string", "content": "string", "status": "string"}, "notifyLink": "/posts/:id"}, "createPostAI_request_2": {"requestId": "createPostAI_request_2", "useWorkflow": {"tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating post content using AI.", "dependencies": [], "next": [{"name": "postWorkflowService.storePost", "uiDescription": "Storing your AI-generated post.", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}]}]}, "useAi": {"generationType": ["text"], "role": "Content Generator", "objective": "Generate a comprehensive and engaging blog post based on the provided topic."}, "dataSchema": "post", "type": "Create", "params": {}, "body": {"topic": "string"}, "notifyLink": "/posts/:id"}}}}