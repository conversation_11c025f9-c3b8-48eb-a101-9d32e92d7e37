{"screenSpecs": {"path": "/posts", "description": "The main screen displaying a scrollable feed of blog posts, including titles, authors, and 'Exclusive' labels for premium content. Allows users to discover content. Main screen displaying a list of blog posts. Presents summaries for all posts, with exclusive posts indicated as premium content", "dataSchemas": {"post": {"owner": true, "canBeCommented": false, "description": "Represents a user's post, containing content, status, and associated metadata.", "fields": {"id": {"type": "String", "required": true, "isUser": false, "description": "Unique identifier for the post."}, "title": {"type": "String", "required": true, "isUser": false, "description": "The title of the post."}, "summary": {"type": "String", "required": true, "isUser": false, "description": "A brief summary of the post content."}, "content": {"type": "String", "required": true, "isUser": false, "description": "The main content of the post."}, "userId": {"type": "String", "required": true, "isUser": false, "description": "The ID of the user who created the post."}, "user": {"type": "User", "required": true, "isUser": false, "description": "The user who owns this post, established by the userId relation."}, "workflowId": {"type": "String", "required": false, "isUser": false, "description": "Optional ID of the associated workflow."}, "isExclusive": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is exclusive."}, "priceCredits": {"type": "Int", "required": true, "isUser": false, "description": "The number of credits required to access the post."}, "status": {"type": "String", "required": true, "isUser": false, "description": "The current status of the post (e.g., 'draft', 'published')."}, "hasAccess": {"type": "Boolean", "required": true, "isUser": false, "description": "Indicates if the post is currently accessible."}, "topic": {"type": "String", "required": true, "isUser": false, "description": "The topic or category of the post."}, "createdAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was created."}, "updatedAt": {"type": "DateTime", "required": true, "isUser": false, "description": "The timestamp when the post was last updated."}}}}, "userExperience": {"ListPosts": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "listPosts_request_1"}}}, {"who": "User", "action": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}}], "ReadPost": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPosts_request_1"}}}, {"who": "User", "action": {"type": "select", "element": {"type": "list:item", "eventId": "post_entry"}}}, {"who": "App", "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}}]}, "requests": {"listPosts_request_1": {"requestId": "listPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find", "params": {}, "body": {}}, "readPosts_request_1": {"requestId": "readPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}}}}