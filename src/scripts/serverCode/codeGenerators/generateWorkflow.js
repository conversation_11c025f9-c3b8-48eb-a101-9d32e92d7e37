import { FileHandler } from "../../utils/FileHandler.js";
import { pathManager } from "../../utils/pathManager.js";
import { generateServiceFiles } from "./generateService.js";
import { generateSwaggerFiles } from "./generateSwagger.js";
import { generateControllerFiles } from "./controller/generateController.js";
import { generateSeeds } from "./generateSeed.js";
import { generateAllStory } from "./generateStory.js";
import { capitalizeFirstLetter } from "../../utils/stringUtils.js";
import { ApplicationError } from "../../utils/CustomError.js";

const fileHandler = new FileHandler();

function filterByUseWorkflow(data) {
  function hasNonEmptyUseWorkflow(obj) {
    return (
      Array.isArray(obj?.useWorkflow?.tasks) && obj.useWorkflow.tasks.length > 0
    );
  }

  function cleanTask(task) {
    const { uiDescription, next, ...rest } = task;
    return {
      ...rest,
      next: Array.isArray(next) ? next.map(cleanTask) : [],
    };
  }

  const result = {};

  for (const [key, items] of Object.entries(data)) {
    const cleanedItems = items.filter(hasNonEmptyUseWorkflow).map((item) => ({
      ...item,
      useWorkflow: {
        ...item.useWorkflow,
        tasks: item.useWorkflow.tasks.map(cleanTask),
      },
    }));

    if (cleanedItems.length > 0) {
      result[key] = cleanedItems;
    }
  }

  return Object.keys(result).length > 0 ? result : {};
}

const updateWorkflowModule = async (key) => {
  try {
    const file = await fileHandler.readFile(
      pathManager.back.modules.workflow.module
    );
    const capitalizedKey = capitalizeFirstLetter(key);
    // Create the controller and service names
    const controllerName = `${capitalizedKey}WorkflowController`;
    const serviceName = `${capitalizedKey}WorkflowService`;

    // Check if imports already exist
    const controllerImportRegex = new RegExp(
      `import\\s*{\\s*${controllerName}\\s*}\\s*from`
    );
    const serviceImportRegex = new RegExp(
      `import\\s*{\\s*${serviceName}\\s*}\\s*from`
    );

    let updatedFile = file;

    // Add imports if they don't exist
    if (!controllerImportRegex.test(updatedFile)) {
      const lastImportMatch = updatedFile.match(
        /import[\s\S]+?from\s+['"].+?['"];\n/g
      );
      if (lastImportMatch) {
        const lastImport = lastImportMatch[lastImportMatch.length - 1];
        const newImport = `import {${controllerName}} from './${key}/${key}.workflow.controller';\n`;
        updatedFile = updatedFile.replace(lastImport, lastImport + newImport);
      }
    }

    if (!serviceImportRegex.test(updatedFile)) {
      const lastImportMatch = updatedFile.match(
        /import[\s\S]+?from\s+['"].+?['"];\n/g
      );
      if (lastImportMatch) {
        const lastImport = lastImportMatch[lastImportMatch.length - 1];
        const newImport = `import {${serviceName}} from './${key}/${key}.workflow.service';\n`;
        updatedFile = updatedFile.replace(lastImport, lastImport + newImport);
      }
    }

    // Add controller to controllers array if it doesn't exist
    const controllersRegex = /controllers:\s*\[([\s\S]*?)\]/;
    const controllersMatch = updatedFile.match(controllersRegex);

    if (controllersMatch) {
      const controllersContent = controllersMatch[1];
      if (!new RegExp(`\\b${controllerName}\\b`).test(controllersContent)) {
        const newControllers = controllersContent.trim().endsWith(",")
          ? `${controllersContent} ${controllerName}`
          : `${controllersContent}, ${controllerName}`;
        updatedFile = updatedFile.replace(
          controllersRegex,
          `controllers: [${newControllers}]`
        );
      }
    }

    // Add service to providers array if it doesn't exist
    const providersRegex = /providers:\s*\[([\s\S]*?)\]/;
    const providersMatch = updatedFile.match(providersRegex);

    if (providersMatch) {
      const providersContent = providersMatch[1];
      if (!new RegExp(`\\b${serviceName}\\b`).test(providersContent)) {
        const newProviders = providersContent.trim().endsWith(",")
          ? `${providersContent} ${serviceName}`
          : `${providersContent}, ${serviceName}`;
        updatedFile = updatedFile.replace(
          providersRegex,
          `providers: [${newProviders}]`
        );
      }
    }

    // Add controller to providers array if it doesn't exist
    const providersRegexForController = /providers:\s*\[([\s\S]*?)\]/;
    const providersMatchForController = updatedFile.match(
      providersRegexForController
    );

    if (providersMatchForController) {
      const providersContent = providersMatchForController[1];
      if (!new RegExp(`\\b${controllerName}\\b`).test(providersContent)) {
        const newProviders = providersContent.trim().endsWith(",")
          ? `${providersContent} ${controllerName}`
          : `${providersContent}, ${controllerName}`;
        updatedFile = updatedFile.replace(
          providersRegexForController,
          `providers: [${newProviders}]`
        );
      }
    }

    // Add controller to exports array if it doesn't exist
    const exportsRegex = /exports:\s*\[([\s\S]*?)\]/;
    const exportsMatch = updatedFile.match(exportsRegex);

    if (exportsMatch) {
      const exportsContent = exportsMatch[1];
      if (!new RegExp(`\\b${controllerName}\\b`).test(exportsContent)) {
        const newExports = exportsContent.trim().endsWith(",")
          ? `${exportsContent} ${controllerName}`
          : `${exportsContent}, ${controllerName}`;
        updatedFile = updatedFile.replace(
          exportsRegex,
          `exports: [${newExports}]`
        );
      }
    }

    // Write the updated file
    await fileHandler.writeText(
      pathManager.back.modules.workflow.module,
      updatedFile
    );

    console.log(
      `Workflow module updated with ${controllerName} and ${serviceName}`
    );
  } catch (error) {
    throw new ApplicationError("Error updating workflow module", {
      originalError: error,
    });
  }
};
const updateWorkerService = async (newServices) => {
  try {
    const filePath = pathManager.back.modules.workflow.workerService;
    let workerService = await fileHandler.readFile(filePath);

    const constructorRegex = /constructor\s*\(([\s\S]*?)\)/;
    const match = workerService.match(constructorRegex);
    if (!match) return; // No constructor found

    // Helper to parse a service string into an object
    const parseService = (service) => {
      const [fullDeclaration, type] = service.split(/:{1,3}\s*/);
      const [accessibility, name] = fullDeclaration.trim().split(/\s+/);
      return {
        accessibility: accessibility || "public",
        name: name || "",
        type: type?.trim() || "",
      };
    };

    // Parse existing and new services
    const existingServices = match[1]
      .split(",")
      .map((param) => param.trim())
      .filter(Boolean)
      .map(parseService)
      .filter((service) => service.name);

    const newServiceObjects = newServices
      .map(parseService)
      .filter((service) => service.name);

    const serviceMap = new Map(existingServices.map((s) => [s.type, s]));
    for (const newService of newServiceObjects) {
      if (!serviceMap.has(newService.type)) {
        serviceMap.set(newService.type, newService);
      }
    }

    // Update constructor
    const updatedConstructorArgs = Array.from(serviceMap.values())
      .map(
        (service) => `${service.accessibility} ${service.name}: ${service.type}`
      )
      .join(",\n");

    workerService = workerService.replace(
      constructorRegex,
      `constructor(\n${updatedConstructorArgs}\n)`
    );

    // Add missing imports
    const existingImports = [
      ...workerService.matchAll(
        /import\s+{([^}]+)}\s+from\s+['"]([^'"]+)['"]/g
      ),
    ];
    const existingImportTypes = new Set(
      existingImports.flatMap(([, types]) =>
        types.split(",").map((t) => t.trim())
      )
    );

    const importStatements = [];
    for (const service of newServiceObjects) {
      if (!existingImportTypes.has(service.type)) {
        // Basic path guess: you may replace with a map or smarter logic if needed
        const entityName = service.name.replace(/WorkflowService$/, "");
        const guessedPath = `./${entityName.replace(/^[a-z]/, (c) =>
          c.toLowerCase()
        )}/${entityName.replace(/^[a-z]/, (c) =>
          c.toLowerCase()
        )}.workflow.service`;

        importStatements.push(
          `import { ${service.type} } from '${guessedPath}';`
        );
      }
    }

    if (importStatements.length > 0) {
      workerService = workerService.replace(
        /(@Injectable\(\))/,
        `${importStatements.join("\n")}\n\n$1`
      );
    }

    await fileHandler.writeText(filePath, workerService);
  } catch (error) {
    throw new ApplicationError("Error updating worker service", {
      originalError: error,
    });
  }
};

export async function generateWorkflows(requests) {
  try {
    const workflowRequests = filterByUseWorkflow(requests);

    for (const key in workflowRequests) {
      await fileHandler.deleteFolder(
        `${pathManager.back.modules.workflow.root}/${key}`
      );
    }
    let services = new Set();
    const resources = Object.keys(workflowRequests);

    resources.forEach(async (key) => {
      const capitalizeKey = capitalizeFirstLetter(key);
      services.add(
        `private ${key}WorkflowService: ${capitalizeKey}WorkflowService`
      );
      await updateWorkflowModule(key);
    });

    const models = await fileHandler.readJson(pathManager.output.models);

    await generateAllStory(requests);
    await generateSeeds(requests);
    await generateControllerFiles(requests, true);
    await generateServiceFiles(workflowRequests, models, true);
    await generateSwaggerFiles(requests, true);
    await updateWorkerService([...services]);
  } catch (error) {
    throw new ApplicationError("Error generating workflows", {
      originalError: error,
    });
  }
}
// const requests = await fileHandler.readJson("specs/requests.json");
// generateWorkflows(requests);
