// serverCode/services/TemplateService.js

import { capitalizeFirstLetter } from "../../utils/stringUtils.js";
import { getWorkflowType } from "../../utils/helpers.js";
import { FileHandler } from "../../utils/FileHandler.js";
import * as ServiceTemplate from "../templates/serviceTemplate.js";
import {
  KEY_FOR_GENERATION_TASK,
  KEY_FOR_STORE_TASK,
  KEY_FOR_UPSCALE_TASK,
  KEY_FOR_EDITING_TASK,
  KEY_FOR_TRANSCRIBE_TASK,
} from "../../utils/constant.js";

import { handleCreateNotification } from "../templates/utilsTemplate.js";

/**
 * TemplateService centralizes code template generation.
 * All templates – whether for controllers, modules, seeds, tests,
 * stories, swagger, workflows, or services – are now maintained here.
 */
export class TemplateService {
  constructor() {
    this.fileHandler = new FileHandler();
  }
  // ========= Controller Templates =========
  // Contains methods for generating controller templates, including CRUD operation snippets.

  /**
   * Generates the top-level controller template.
   * This method injects required imports, constructor parameters, and CRUD operations.
   *
   * @param {string} swaggerDependencies - Swagger-related import statements.
   * @param {string} crudOperations - CRUD operations methods (as string snippets).
   * @param {Object} options - Options object containing resourceKey, isAi, isWorkflow, etc.
   * @returns {string} - The complete controller template as a string.
   */
  static generateControllerTemplate(
    swaggerDependencies,
    crudOperations,
    options
  ) {
    // Capitalize the resource name for consistency
    const capitalizedResource = capitalizeFirstLetter(options.resourceKey);

    // AI-specific configuration
    const aiConfig = {
      imports: `
        import { AiService, DEFAULT_IMAGE_MODEL } from 'src/shared/ai/ai.service';
        import { extractJson } from 'src/shared/format/helpers';
        import { ProviderType } from 'src/shared/ai/ai.type';
        import { ConfigService } from 'src/shared/config/config.service';
        import { ImagePrompt, VideoPrompt } from 'specs/prompt';
        import { extractText } from 'src/bot/extractors';
        import path from 'path';
        import { downloadImage } from 'src/shared/format/images';
         import { downloadMedia, getFileMetadata } from 'src/shared/format/media';
        import {promises as fs} from 'fs';
        import { FileService } from 'src/shared/file/file.service';
        import {NotificationService} from 'src/shared/notification/notification.service';
        import Notifications from 'src/shared/notification/notifications';
        import {NOTIFICATION_CONFIG} from 'src/shared/config/notification.config';
        import moment from 'moment';

              `,
      constructorParams: `
        private aiService: AiService,
        private config: ConfigService,
        public fileService: FileService,
        public notificationService: NotificationService,
      `,
    };

    // Workflow-specific configuration
    const workflowConfig = {
      imports: `
        import { ${capitalizedResource}WorkflowService } from './${options.resourceKey}.workflow.service';
        import { WorkflowService } from '../workflow.service';
              `,
      constructorParams: `
        private ${options.resourceKey}WorkflowService: ${capitalizedResource}WorkflowService,
        private workflowService: WorkflowService
      `,
    };

    // Determine final imports and constructor parameters based on options
    const finalImports = options.isAi ? aiConfig.imports : "";
    const finalConstructor = options.isWorkflow
      ? workflowConfig.constructorParams
      : `private readonly ${options.resourceKey}Service: ${capitalizedResource}Service`;

    const workflowImports = options.isWorkflow
      ? workflowConfig.imports
      : `import { ${capitalizedResource}Service } from './${options.resourceKey}.service';`;

    return `
        import {
          Body,
          Controller,
          Delete as NestDelete,
          Get as NestGet,
          NotFoundException,
          Param,
          Patch as NestPatch,
          Post as NestPost,
          UseGuards,
          UseInterceptors,
          UploadedFile,
        } from '@nestjs/common';
        import {
          ApiBearerAuth,
          ApiOperation,
          ApiResponse,
          ApiConsumes,
          ApiTags,
        } from '@nestjs/swagger';
        import { AccessTo, AccessCreditNeeded} from 'src/acl/acl.decorator';
        import { AccessGuard } from 'src/acl/acl.guard';
        import { ${capitalizedResource}, Prisma, User } from '@prisma/client';
        import { AuthGuard } from 'src/modules/auth/auth.guard';
        import { ReqUser } from 'src/modules/auth/auth.decorator';
        import * as story from 'specs/story/${options.resourceKey}';
        import {ApiFile} from 'src/shared/swagger/swagger.decorator';
        import {FileInterceptor} from '@nestjs/platform-express';
        import {Upload} from 'src/shared/file/file.interface';

        ${swaggerDependencies}
        ${finalImports}
        ${workflowImports}

        @Controller("${
          options.isWorkflow
            ? "workflow/" + options.resourceKey
            : options.resourceKey
        }" )
        @ApiTags("${options.isWorkflow ? "workflow" : options.resourceKey}")
        @ApiBearerAuth()
        export class ${
          options.isWorkflow
            ? capitalizedResource + "WorkflowController"
            : capitalizedResource + "Controller"
        } {
          constructor(
            ${options.isAi ? aiConfig.constructorParams : ""}
            ${finalConstructor}
          ) {}

          ${crudOperations}
        }
`;
  }

  /**
   * Handles the creation operation snippet.
   * Generates the controller method for creating a resource.
   *
   * @param {Object} params - An object containing aiConfig, workflowConfig, capitalizedResource, resourceKey.
   * @returns {string} - The create operation method as a string.
   */
  static handleCreateOperation({
    requestConfigs,
    capitalizedResource,
    resourceKey,
    notification,
  }) {
    const { aiConfig, workflowConfig } = requestConfigs;
    const templateKey = () => {
      const workflowType = getWorkflowType(workflowConfig.steps);
      switch (workflowType) {
        case `${KEY_FOR_GENERATION_TASK}+${KEY_FOR_UPSCALE_TASK}+${KEY_FOR_STORE_TASK}`:
          return "generateAndUpscaleWorkflow";
        case `${KEY_FOR_UPSCALE_TASK}+${KEY_FOR_STORE_TASK}`:
          return "uploadAndUpscaleWorkflow";
        case `${KEY_FOR_EDITING_TASK}+${KEY_FOR_STORE_TASK}`:
          return "editWorkflow";
        case `${KEY_FOR_TRANSCRIBE_TASK}+${KEY_FOR_STORE_TASK}`:
          return "transcribeWorkflow";
      }
      return "defaultWorkflow";
    };

    // Define all possible templates in an object
    const templates = {
      transcribeWorkflow: `
      @NestPost('/transcribe')
      @ApiFile('file')
      @ApiConsumes('multipart/form-data')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.${resourceKey}ToUploadAndTranscribe.access)
      @ApiOperation(story.${resourceKey}ToUploadAndTranscribe.operation)
      @ApiResponse(story.${resourceKey}ToUploadAndTranscribe.codes['201'].response)
      @ApiResponse(story.${resourceKey}ToUploadAndTranscribe.codes['401'].response)
      @UseInterceptors(FileInterceptor('file', {dest: '/tmp/'}))
      @AccessCreditNeeded(${JSON.stringify(
        workflowConfig?.accessCredit?.consumes ?? { audio: 1 }
      )})
      async transcribe(
        @ReqUser() user: User,
        @UploadedFile('file') media: Upload,
        @Body('fileUrl') fileUrl?: string,
      ): Promise<string> {
        const fileUploaded = await this.fileService.uploadMedia({
          folder: '${resourceKey}s',
          file: media || fileUrl,
        });

        const workflow: any = await this.workflowService.create(
          user.id,
          user.id,
          'transcribe${capitalizedResource}',
          {user, input: fileUploaded.url, fileUploaded},
          ${JSON.stringify(workflowConfig.tasks)},
          {
            ownerId: user.id,
            actionType: 'transcribe${capitalizedResource}',
            ownerType: '${resourceKey}',
            objectName: '${resourceKey}' + moment(),
          },
        );
        ${notification}
        return workflow.workflowId;
      }
    `,
      defaultWorkflow: `
      @NestPost('/create')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.${resourceKey}ToCreate.access)
      @ApiOperation(story.${resourceKey}ToCreate.operation)
      @ApiResponse(story.${resourceKey}ToCreate.codes['201'].response)
      @ApiResponse(story.${resourceKey}ToCreate.codes['401'].response)
      ${
        aiConfig.useAi
          ? `@AccessCreditNeeded(${JSON.stringify(
              workflowConfig?.accessCredit?.consumes ?? { text: 1 }
            )})`
          : ""
      }
      async create(
        @ReqUser() user: User,
        @Body() ${resourceKey}: Create${capitalizedResource}Params,
      ): Promise<string> {
        const workflow: any = await this.workflowService.create(
          user.id,
          user.id,
          'generate${capitalizedResource}',
          { user , input:${resourceKey}, prompt: ${resourceKey}.${
        aiConfig.key
      } },
          ${JSON.stringify(workflowConfig.tasks)},
          {
            ownerId: user.id,
            actionType: 'generate${capitalizedResource}',
            ownerType: '${resourceKey}',
            objectName: '${resourceKey}' + moment(),
          },
        );
        ${notification}
        return workflow.workflowId;
      }
    `,
      generateAndUpscaleWorkflow: `
      @NestPost('/upscale')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.${resourceKey}ToUpscale.access)
      @ApiOperation(story.${resourceKey}ToUpscale.operation)
      @ApiResponse(story.${resourceKey}ToUpscale.codes['201'].response)
      @ApiResponse(story.${resourceKey}ToUpscale.codes['401'].response)
      @AccessCreditNeeded(${JSON.stringify(
        workflowConfig?.accessCredit?.consumes ?? { text: 1, image: 1 }
      )})
      async create(
        @ReqUser() user: User,
        @Body() ${resourceKey}: Create${capitalizedResource}Params,
      ): Promise<string> {

        const workflow: any = await this.workflowService.create(
          user.id,
          user.id,
          'upscale${capitalizedResource}',
          { user , input:${resourceKey}.topic },
          ${JSON.stringify(workflowConfig.tasks)},
          {
            ownerId: user.id,
            actionType: 'upscale${capitalizedResource}',
            ownerType: '${resourceKey}',
            objectName: '${resourceKey}' + moment(),
          },
        );
        ${notification}
        return workflow.workflowId;
      }
    `,
      uploadAndUpscaleWorkflow: `
      @NestPost('/upscale')
      @ApiConsumes('multipart/form-data')
      @ApiFile('file')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.${resourceKey}ToUpscale.access)
      @ApiOperation(story.${resourceKey}ToUpscale.operation)
      @ApiResponse(story.${resourceKey}ToUpscale.codes['201'].response)
      @ApiResponse(story.${resourceKey}ToUpscale.codes['401'].response)
      @UseInterceptors(FileInterceptor('file', {dest: '/tmp/'}))
      @AccessCreditNeeded(${JSON.stringify(
        workflowConfig?.accessCredit?.consumes ?? { image: 1 }
      )})
      async create(
        @ReqUser() user: User,
        @UploadedFile('file') media?: Upload,
        @Body('fileUrl') fileUrl?: string,
      ): Promise<string> {

      const fileUploaded = await this.fileService.uploadMedia({
        folder: 'images',
        file: media || fileUrl,
        width: 1920,
        height: 1080,
        thumbWidth: 800,
        thumbHeight: 800,
      });

        const workflow: any = await this.workflowService.create(
          user.id,
          user.id,
          'upscale${capitalizedResource}',
          { user , input: fileUploaded },
          ${JSON.stringify(workflowConfig.tasks)},
          {
            ownerId: user.id,
            actionType: 'upscale${capitalizedResource}',
            ownerType: '${resourceKey}',
            objectName: '${resourceKey}' + moment(),
          },
        );
        ${notification}
        return workflow.workflowId;
      }
    `,
      editWorkflow: `
    @NestPost('/edit')
    @ApiConsumes('multipart/form-data')
    @UseGuards(AuthGuard, AccessGuard)
    @AccessTo(story.${resourceKey}ToEdit.access)
    @ApiOperation(story.${resourceKey}ToEdit.operation)
    @ApiResponse(story.${resourceKey}ToEdit.codes['201'].response)
    @ApiResponse(story.${resourceKey}ToEdit.codes['401'].response)
    @UseInterceptors(FileInterceptor('file', {dest: '/tmp/'}))
     @AccessCreditNeeded(${JSON.stringify(
       workflowConfig?.accessCredit?.consumes ?? { image: 1 }
     )})
    async create(
      @ReqUser() user: User,
      @Body() params: Create${capitalizedResource}Params,
      @UploadedFile('file') media: Upload,
    ): Promise<string> {

    const fileUploaded = await this.fileService.uploadMedia({
      folder: '${resourceKey}',
      file: media,
    });

      const workflow: any = await this.workflowService.create(
        user.id,
        user.id,
        'edit${capitalizedResource}',
        { user , input: fileUploaded.url, prompt: params.topic},
        ${JSON.stringify(workflowConfig.tasks)},
        {
          ownerId: user.id,
          actionType: 'edit${capitalizedResource}',
          ownerType: '${resourceKey}',
          objectName: '${resourceKey}' + moment(),
        },
      );
      ${notification}
      return workflow.workflowId;
    }
  `,
    };

    return templates[templateKey()];
  }

  /**
   * Generates individual CRUD operation methods for a resource.
   * Depending on the operation, returns corresponding method templates.
   *
   * @param {string} operation - The CRUD operation type (e.g., "create", "read", "update", "delete", "find").
   * @param {string} whereCondition - Conditions used for filtering (in "find" operation).
   * @param {Object} ai - AI configuration object.
   * @param {Object} image - Image configuration object.
   * @param {Object} workflowConfig - Workflow configuration object.
   * @param {string} resourceKey - The resource identifier.
   * @returns {string} - The concatenated CRUD operation methods as a string.
   */
  static getMethods(
    operation,
    whereCondition,
    requestConfigs,
    resourceKey,
    notificationFunction
  ) {
    const { image, workflowConfig } = requestConfigs;
    const capitalizedResource = capitalizeFirstLetter(resourceKey);

    // UNCOMMENT WHEN NOTIFICATION IS READY
    // const notification = notificationFunction
    //   ? handleCreateNotification(
    //       notificationFunction,
    //       Object.keys(workflowConfig).length > 0
    //     )
    //   : "";

    const notification = "";
    // Create snippet for "create" operation.
    const createSnippet =
      operation === "create"
        ? TemplateService.handleCreateOperation({
            requestConfigs,
            capitalizedResource,
            resourceKey,
            notification,
          })
        : "";

    // Create snippet for "find" operation.
    const findSnippet =
      operation === "find"
        ? `
      whereToPrisma(params): Prisma.${capitalizedResource}WhereInput {
        let where: Prisma.${capitalizedResource}WhereInput = {};
        const OR = [];
        ${whereCondition}
        if (OR.length > 0) {
          where.OR = OR;
        }
        return where;
      }

      @NestPost('find')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.${resourceKey}ToFind.access)
      @ApiOperation(story.${resourceKey}ToFind.operation)
      @ApiResponse(story.${resourceKey}ToFind.codes['201'].response)
      async find(@ReqUser() user: User,@Body() params: Find${capitalizedResource}Params): Promise<${capitalizedResource}[]> {
        const { take, skip, sortField = 'id', sortOrder = 'asc', ...where } = params;

        const currentData = await this.${resourceKey}WorkflowService.find({
          where: this.whereToPrisma(where),
          orderBy: { [sortField]: sortOrder },
          take,
          skip,
        });

        if(currentData && currentData.length > 0){
        ${notification}
        }
        return currentData;
      }
`
        : "";

    // Create snippet for "read" operation.
    const readSnippet =
      operation === "read"
        ? `
        @NestGet(':id')
        @UseGuards(AuthGuard, AccessGuard)
        @AccessTo(story.${resourceKey}ToRead.access)
        @ApiOperation(story.${resourceKey}ToRead.operation)
        @ApiResponse(story.${resourceKey}ToRead.codes['200'].response)
        @ApiResponse(story.${resourceKey}ToRead.codes['401'].response)
        @ApiResponse(story.${resourceKey}ToRead.codes['404'].response)
        async read(@ReqUser() user: User, @Param('id') id: string): Promise<${capitalizedResource}> {
          const currentData = await this.${resourceKey}WorkflowService.get({ id });
          if (!currentData) {
            throw new NotFoundException();
          }
          ${notification}
          return currentData;
        }
`
        : "";

    // Create snippet for "delete" operation.
    const deleteSnippet =
      operation === "delete"
        ? `
      @NestDelete(':id')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.${resourceKey}ToDelete.access)
      @ApiOperation(story.${resourceKey}ToDelete.operation)
      @ApiResponse(story.${resourceKey}ToDelete.codes['200'].response)
      @ApiResponse(story.${resourceKey}ToDelete.codes['403'].response)
      @ApiResponse(story.${resourceKey}ToDelete.codes['404'].response)
      async delete(@Param('id') id: string): Promise<boolean> {
        const currentData = await this.${resourceKey}WorkflowService.delete({ id });
        if (!currentData) {
          throw new NotFoundException();
        }
        ${notification}
        return true;
      }
`
        : "";

    // Create snippet for "update" operation.
    let updateSnippet = "";
    if (operation === "update") {
      // Update operation with image configuration
      if (image.useImage) {
        updateSnippet = `
      @NestPatch(':id')
      @UseGuards(AuthGuard, AccessGuard)
      @AccessTo(story.${resourceKey}ToUpdate.access)
      @ApiOperation(story.${resourceKey}ToUpdate.operation)
      @ApiResponse(story.${resourceKey}ToUpdate.codes['200'].response)
      @ApiResponse(story.${resourceKey}ToUpdate.codes['401'].response)
      @ApiResponse(story.${resourceKey}ToUpdate.codes['404'].response)
      async updateImage(
        @ReqUser() user: User,
        @Param('id') id: string,
        @Body() params: Update${capitalizedResource}Params,
      ): Promise<${capitalizedResource}> {
        const resource = await this.${resourceKey}WorkflowService.get({id});
        let imageSpec = await this.aiService.send({
          user,
          messages: [{
            role: 'user',
            content: ImagePrompt({ context: ${image.resourceParams}, imageType: 'photography' }),
          }],
        });
        imageSpec = extractText(imageSpec);
        const images: any = await this.aiService.send({
          user,
          type:"image",
          messages: [{
            role: 'user',
            content: imageSpec,
          }],
        });
        if (images.length > 0) {
          const imageFolder = path.join('/tmp', 'images', resource.id);
          await fs.mkdir(imageFolder, {recursive: true});
          const imagePath = await downloadImage(images[0], imageFolder);
          const fileMeta = getFileMetadata(imagePath);
          const fileUploaded = await this.fileService.uploadFile({
            file: fileMeta,
            folder: 'images',
            type: 'image',
          });
          params.{{imageModelKey}} = fileUploaded.url;
        }
        const currentData = await this.${resourceKey}Service.update({id}, params);
        if (!currentData) {
          throw new NotFoundException();
        }
        ${notification}
        return currentData;
      }
      `;
      } else {
        // Update operation without image handling
        updateSnippet = `
        @NestPatch(':id')
        @UseGuards(AuthGuard, AccessGuard)
        @AccessTo(story.${resourceKey}ToUpdate.access)
        @ApiOperation(story.${resourceKey}ToUpdate.operation)
        @ApiResponse(story.${resourceKey}ToUpdate.codes['200'].response)
        @ApiResponse(story.${resourceKey}ToUpdate.codes['401'].response)
        @ApiResponse(story.${resourceKey}ToUpdate.codes['404'].response)
        async update(
          @ReqUser() user: User,
          @Param('id') id: string,
          @Body() params: Update${capitalizedResource}Params,
        ): Promise<${capitalizedResource}> {
          const currentData = await this.${resourceKey}WorkflowService.update({ id }, params);
          if (!currentData) {
            throw new NotFoundException();
          }
          ${notification}
          return currentData;
        }
`;
      }
    }
    // Concatenate all snippets and return the complete method definitions.
    return `
    ${createSnippet}
    ${findSnippet}
    ${readSnippet}
    ${deleteSnippet}
    ${updateSnippet}
`;
  }

  // ========= Workflow Templates =========
  // Contains methods for generating workflow-related tasks and functions.

  /**
   * Returns the appropriate workflow task template based on the parameters.
   *
   * @param {string} name - Name of the workflow task.
   * @param {string} resource - The resource identifier (e.g., "post", "video").
   * @param {Object} requestConfigs - Configuration object.
   * @returns {string} - The workflow task template as a string.
   */
  static getWorkflowTaskTemplate = async (name, resource, requestConfigs) => {
    return await ServiceTemplate.getWorkerTaskTemplate(
      name,
      resource,
      requestConfigs
    );
  };
  /**
   * Generates a workflow function template.
   *
   * @param {Object} params - Object containing name, functionContent, and type.
   * @returns {string} - The workflow function template as a string.
   */
  static generateWorkflowFunctionTemplate({ name, functionContent, type }) {
    return ServiceTemplate.generateWorkerFunctionTemplate({
      name,
      functionContent,
      type,
    });
  }

  // ========= Service Template =========
  // Contains methods for generating service templates and CRUD methods implementation.
  /**
   * Generates the service template for a resource.
   *
   * @param {Object} params - Contains capitalizedResource, isWorkflow, and resourceKey.
   * @returns {string} - The complete service template as a string.
   */
  static generateServiceTemplate({ capitalizedResource, resourceKey }) {
    return ServiceTemplate.generateServiceTemplate({
      capitalizedResource,
      resourceKey,
    });
  }

  // ========= Module Template =========
  // Contains methods for generating module templates.

  /**
   * Generates the module template for a resource.
   *
   * @param {Object} params - Contains resourceKey, capitalizedResource, resourcePath, useAi.
   * @returns {string} - The complete module template as a string.
   */
  static generateModuleTemplate({
    resourceKey,
    capitalizedResource,
    resourcePath,
    useAi,
  }) {
    // Define base import statements
    const baseImports = [
      `import { Module } from '@nestjs/common';`,
      `import { ${capitalizedResource}Controller } from '../${resourcePath}/${resourceKey}.controller';`,
      `import { ${capitalizedResource}Service } from '../${resourcePath}/${resourceKey}.service';`,
      `import {NotificationService} from 'src/shared/notification/notification.service';`,
    ];
    if (useAi) {
      baseImports.push(
        `import { AiService } from 'src/shared/ai/ai.service';`,
        `import { FileService } from 'src/shared/file/file.service';`
      );
    }
    // Define providers list
    const providers = [
      `${capitalizedResource}Service`,
      `${capitalizedResource}Controller`,
      "NotificationService",
      ...(useAi ? ["AiService", "FileService"] : []),
    ].join(", ");
    return `
${baseImports.join("\n")}

@Module({
  imports: [],
  controllers: [${capitalizedResource}Controller],
  providers: [${providers}],
  exports: [${capitalizedResource}Service, ${capitalizedResource}Controller],
})
export class ${capitalizedResource}Module {}
`;
  }

  // ========= Seed Template =========
  // Contains methods for generating seed templates.

  /**
   * Generates the seed template for a resource.
   *
   * @param {Object} params - Contains fileName, capitalizedResource, seedParams.
   * @returns {string} - The complete seed template as a string.
   */
  static generateSeedTemplate({ fileName, capitalizedResource, seedParams }) {
    return `
import { SeedSpec } from 'specs/specs.interface';

export const ${fileName}: SeedSpec = [
  {
    provider: '${capitalizedResource}WorkflowController',
    action: 'create',
    params: [
      'auth.test',
      ${seedParams},
    ],
  },
];
`;
  }

  // ========= Test Template =========
  // Contains methods for generating test templates.

  /**
   * Generates the test template for a resource.
   *
   * @param {Object} params - Contains resource, capitalizedResource.
   * @returns {string} - The complete test template as a string.
   */
  static generateTestTemplate({ resource, capitalizedResource }) {
    return `
import { testStory } from 'src/shared/test/test.utils';
import * as story from 'specs/story/${resource}';
describe('${capitalizedResource}', () => {
  for (const storyName in story) {
    testStory(story[storyName]);
  }
});
`;
  }

  // ========= Story Template =========
  // Contains methods for generating story templates and objects.

  /**
   * Generates the story template for a resource operation.
   *
   * @param {Object} params - Contains resource, operationName, capitalizedResource, route, method, access, codes.
   * @returns {string} - The complete story template as a string.
   */
  static generateStoryTemplate({
    resource,
    operationName,
    capitalizedResource,
    route,
    method,
    access,
    codes,
  }) {
    return `
import { StorySpec } from 'specs/specs.interface';

export const ${resource}To${capitalizedResource}: StorySpec = {
  filePath: __filename,
  route: '${route}',
  method: '${method}',
  operation: {
    summary: 'As a user, I want to ${operationName} a ${resource}',
  },
  access: {
    resource: '${resource}',
    action: '${operationName === "find" ? "read" : operationName}',
    owner: false
  },
  codes: ${codes}
};
`;
  }

  /**
   * Generates the story object for a specific request.
   *
   * @param {string} baseAuth - The base authentication string (e.g., "user").
   * @param {Object} additionalFields - Additional fields to include in the story.
   * @param {Object} request - The request object containing body and params.
   * @param {boolean} hasBody - Flag to check if request body exists.
   * @param {boolean} hasParams - Flag to check if request params exist.
   * @returns {string} - The formatted story object as a string.
   */
  static generateStoryObject(
    baseAuth,
    additionalFields = {},
    request,
    hasBody,
    hasParams
  ) {
    let story = `{
        auth: "${baseAuth}",`;
    if (hasParams) {
      story += `
        path: ${JSON.stringify(request.params)},`;
    }
    if (hasBody) {
      story += `
        body: ${JSON.stringify(request.body)},`;
    }
    for (const [key, value] of Object.entries(additionalFields)) {
      story += `
        ${key}: '${value}',`;
    }
    story =
      story.trim().replace(/,$/, "") +
      `
      }`;
    return story;
  }

  /**
   * Generates codes sections for different HTTP methods.
   * Builds response objects for Swagger documentation based on the operation.
   *
   * @param {Object} params - Contains resource, operationName, storyObject, generateTestsArray.
   * @returns {Object} - An object with keys POST, DELETE, GET, PATCH, each containing response code configurations.
   */
  static async generateCodesSections({
    resource,
    operationName,
    storyObject,
    generateTestsArray,
  }) {
    return {
      POST: `{
        '201': {
          response: {
            status: 201,
            description: '${resource} ${
        operationName === "find" ? "found" : "created"
      }',
          },
          story: ${storyObject},
          tests: ${await generateTestsArray("201")},
        },
        '401': {
          response: {
            status: 401,
            description: 'Incorrect credentials',
          },
        },
        ${
          operationName === "find"
            ? `      '404': {
          response: {
            status: 404,
            description: "${resource} doesn't exist",
          },
        },`
            : ""
        }
      },`,
      DELETE: `{
        '200': {
          response: {
            status: 200,
            description: '${resource} deleted',
          },
          story: ${storyObject},
          tests: ${await generateTestsArray("200")},
        },
        '403': {
          response: {
            status: 403,
            description: 'Forbidden: Incorrect credentials',
          },
          story: ${storyObject},
        },
        '404': {
          response: {
            status: 404,
            description: "${resource} doesn't exist",
          },
          story: ${storyObject},
        },
      },`,
      GET: `{
        '200': {
          response: {
            status: 200,
            description: "${resource}'s data",
          },
          story: ${storyObject},
          tests: ${await generateTestsArray("200")},
        },
        '401': {
          response: {
            status: 401,
            description: 'Incorrect credentials',
          },
        },
        '404': {
          response: {
            status: 404,
            description: "${resource} doesn't exist",
          },
        },
      },`,
      PATCH: `{
        '200': {
          response: {
            status: 200,
            description: '${resource} Updated',
          },
          story: ${storyObject},
          tests: ${await generateTestsArray("200")},
        },
        '401': {
          response: {
            status: 401,
            description: 'Incorrect credentials',
          },
        },
        '404': {
          response: {
            status: 404,
            description: "${resource} doesn't exist",
          },
          story: ${storyObject},
        },
      },`,
    };
  }

  // ========= Swagger Template =========
  // Contains methods for generating Swagger documentation class definitions.

  /**
   * Generates a class definition for Swagger DTOs including decorators.
   *
   * @param {Object} params - Contains operation, resource, requestBody, models.
   * @returns {string} - The Swagger DTO class definition as a string.
   */
  static generateClassDefinition({ operation, resource, requestBody, models }) {
    let classDefinition = `export class ${capitalizeFirstLetter(
      operation
    )}${capitalizeFirstLetter(resource)}Params {\n`;

    for (let [fieldName, exampleValue] of Object.entries(requestBody)) {
      // For the 'find' operation, set example value using seed data.
      if (operation === "find") {
        exampleValue = `seed.${resource}To${capitalizeFirstLetter(
          operation
        )}.result.${fieldName}`;
      } else {
        // For string fields in other operations, use default mock value.
        const modelField = models[resource]?.fields[fieldName] || null;
        if (modelField) {
          if (modelField.type && modelField.type.toLowerCase() === "string") {
            exampleValue = "mock.word";
          }
        } else {
          exampleValue = "mock.default";
        }
      }

      const modelField = models[resource]?.fields[fieldName] || null;
      if (!modelField) continue; // Skip if no model field exists

      const { type, required, description } = modelField;

      if (!type) {
        console.warn(
          `Type not found for field ${fieldName} in resource ${resource}. Skipping.`
        );
        continue;
      }

      // Get the proper validator decorator based on type.
      const validatorDecorator = TemplateService.getValidatorDecorator(type);

      const isOptional = !required;

      // Build decorators for Swagger and class-validator.
      let decorators = `
        @ApiProperty({
          required: ${required},
          description: '${description || "No description available"}',
          example: '${exampleValue}',
        })\n`;

      decorators += isOptional ? "  @IsOptional()\n" : "  @IsNotEmpty()\n";
      decorators += `  @${validatorDecorator}\n  ${fieldName}: ${
        type.toLowerCase() === "datetime"
          ? "Date"
          : type.toLowerCase() === "float"
          ? "Number"
          : type.toLowerCase()
      };\n\n`;

      classDefinition += decorators.replace("float", "Number");
    }

    if (operation === "find") {
      // Append pagination and sorting fields for "find" operations.
      classDefinition += `
        @ApiProperty({
          required: false,
          description: 'Order by field',
          enum: Object.keys(Prisma.${capitalizeFirstLetter(
            resource
          )}ScalarFieldEnum),
        })
        @IsEnum(Object.keys(Prisma.${capitalizeFirstLetter(
          resource
        )}ScalarFieldEnum))
        @IsOptional()
        sortField: string;

        @ApiProperty({
          required: false,
          description: 'Order sort',
          enum: Prisma.SortOrder,
        })
        @IsEnum(Prisma.SortOrder)
        @IsOptional()
        sortOrder: Prisma.SortOrder;

        @ApiProperty({
          required: false,
          description: 'Number of results to return',
          example: 10,
        })
        @IsOptional()
        @IsNumber()
        take: number;

        @ApiProperty({
          required: false,
          description: 'Number of results to skip',
          example: 0,
        })
        @IsOptional()
        @IsNumber()
        skip: number;
      `;
    }

    classDefinition += `}\n\n`;

    return classDefinition;
  }

  /**
   * Returns the proper class-validator decorator based on the type.
   *
   * @param {string} type - The field type.
   * @returns {string} - The class-validator decorator as a string.
   */
  static getValidatorDecorator(type) {
    switch (type.toLowerCase()) {
      case "string":
        return "IsString()";
      case "number":
      case "int":
      case "float":
        return "IsNumber()";
      case "boolean":
        return "IsBoolean()";
      case "object":
        return "IsObject()";
      case "datetime":
        return "IsDateString()";
      default:
        return "IsString()";
    }
  }

  /**
   * Checks if a class definition exists in the provided file content.
   *
   * @param {string} fileContent - The content of the file.
   * @param {string} className - The name of the class to search for.
   * @returns {boolean} - True if the class exists, false otherwise.
   */
  static doesClassExist(fileContent, className) {
    const regex = new RegExp(`export\\s+class\\s+${className}\\s+{`);
    return regex.test(fileContent);
  }

  /**
   * Generates the Swagger class definition for DTOs.
   * Builds the class with @ApiProperty and class-validator decorators.
   *
   * @param {Object} params - Contains resource, operation, requestBody, models.
   * @returns {string} - The Swagger class definition as a string.
   */
  static generateSwaggerClassDefinition({
    resource,
    operation,
    requestBody,
    models,
  }) {
    const className = `${operation}${capitalizeFirstLetter(resource)}Params`;
    let classDefinition = `export class ${capitalizeFirstLetter(
      className
    )} {\n`;

    for (let [fieldName, exampleValue] of Object.entries(requestBody)) {
      // Override example value for "find" operation using seed data.
      if (operation === "find") {
        exampleValue = `seed.${resource}To${capitalizeFirstLetter(
          operation
        )}.result.${fieldName}`;
      } else {
        // For string fields, use default mock value.
        if (
          models[resource]?.fields[fieldName]?.type?.toLowerCase() === "string"
        ) {
          exampleValue = "mock.word";
        }
      }

      if (fieldName === "file") {
        // Append decorators and field definition.
        classDefinition += `
        @ApiProperty({
          required: true,
          description: 'Image file to edit',
          type: 'string',
          format: 'binary',
        })
        file: File;
      `;
      }

      // Skip field if it doesn't exist in the model.
      const modelField = models[resource]?.fields[fieldName];
      if (!modelField) continue;

      let { type, required, description } = modelField;
      const isOptional = !required;
      const validatorDecorator = TemplateService.getValidatorDecorator(type);

      type =
        type.toLowerCase() === "datetime"
          ? "Date"
          : type.toLowerCase() === "float"
          ? "Number"
          : type.toLowerCase();
      // Append decorators and field definition.
      classDefinition += `
      @ApiProperty({
        required: ${required},
        description: '${description}',
        example: '${exampleValue}',
      })
      ${isOptional ? "@IsOptional()\n" : "@IsNotEmpty()\n"}
      @${validatorDecorator}
      ${fieldName}: ${
        type.toLowerCase() === "datetime" ? "Date" : type.toLowerCase()
      };
      `;
    }

    // Append pagination and sorting fields for "find" operation.
    if (operation === "find") {
      classDefinition += `
      @ApiProperty({
        required: false,
        description: 'Order by field',
        enum: Object.keys(Prisma.${capitalizeFirstLetter(
          resource
        )}ScalarFieldEnum),
      })
      @IsEnum(Object.keys(Prisma.${capitalizeFirstLetter(
        resource
      )}ScalarFieldEnum))
      @IsOptional()
      sortField: string;

      @ApiProperty({
        required: false,
        description: 'Order sort',
        enum: Prisma.SortOrder,
      })
      @IsEnum(Prisma.SortOrder)
      @IsOptional()
      sortOrder: Prisma.SortOrder;

      @ApiProperty({
        required: false,
        description: 'Number of results to return',
        example: 10,
      })
      @IsOptional()
      @IsNumber()
      take: number;

      @ApiProperty({
        required: false,
        description: 'Number of results to skip',
        example: 0,
      })
      @IsOptional()
      @IsNumber()
      skip: number;
      `;
    }

    classDefinition += `}\n\n`;
    return classDefinition;
  }
}
